using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Infrastructure.Repositories
{
    public class PropertyRepository : AuditableRepository<Property>, IPropertyRepository
    {
        public PropertyRepository(ApplicationDbContext context) : base(context) {}

        public async Task<IEnumerable<Property>> GetPropertyByUserAsync(Guid userId)
        {
            return await _dbSet
            .Where(bp => bp.CreatedBy == userId && !bp.IsDeleted)
            .ToListAsync();
        }

        public async Task<Property?> GetPropertyBySlugAsync(string slug)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Slug == slug && !p.IsDeleted);
        }
    }
}
